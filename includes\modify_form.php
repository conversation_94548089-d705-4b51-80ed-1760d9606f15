<?php
require_once '../config/database.php';

header('Content-Type: application/json');

class FormModifier {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function updateField($fieldId, $updates) {
        try {
            $allowedFields = [
                'field_name', 'field_type', 'field_placeholder', 'field_value',
                'field_required', 'field_readonly', 'field_disabled',
                'field_min', 'field_max', 'field_step', 'field_pattern',
                'field_maxlength', 'field_minlength', 'field_options'
            ];

            $setParts = [];
            $values = [];

            foreach ($updates as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $setParts[] = "$field = ?";
                    $values[] = $value;
                }
            }

            if (empty($setParts)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }

            $values[] = $fieldId;
            $sql = "UPDATE form_fields SET " . implode(', ', $setParts) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($values);

            if ($result) {
                return ['success' => true, 'message' => 'Field updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update field'];
            }

        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function addField($formId, $fieldData) {
        try {
            $sql = "INSERT INTO form_fields (
                form_id, field_name, field_type, field_id, field_class, 
                field_placeholder, field_value, field_required, field_readonly, 
                field_disabled, field_multiple, field_min, field_max, field_step, 
                field_pattern, field_maxlength, field_minlength, field_size, 
                field_rows, field_cols, field_accept, field_autocomplete, field_options
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                $formId,
                $fieldData['field_name'] ?? '',
                $fieldData['field_type'] ?? 'text',
                $fieldData['field_id'] ?? null,
                $fieldData['field_class'] ?? null,
                $fieldData['field_placeholder'] ?? null,
                $fieldData['field_value'] ?? null,
                isset($fieldData['field_required']) ? 1 : 0,
                isset($fieldData['field_readonly']) ? 1 : 0,
                isset($fieldData['field_disabled']) ? 1 : 0,
                isset($fieldData['field_multiple']) ? 1 : 0,
                $fieldData['field_min'] ?? null,
                $fieldData['field_max'] ?? null,
                $fieldData['field_step'] ?? null,
                $fieldData['field_pattern'] ?? null,
                $fieldData['field_maxlength'] ?? null,
                $fieldData['field_minlength'] ?? null,
                $fieldData['field_size'] ?? null,
                $fieldData['field_rows'] ?? null,
                $fieldData['field_cols'] ?? null,
                $fieldData['field_accept'] ?? null,
                $fieldData['field_autocomplete'] ?? null,
                isset($fieldData['field_options']) ? json_encode($fieldData['field_options']) : null
            ]);

            if ($result) {
                return [
                    'success' => true, 
                    'message' => 'Field added successfully',
                    'field_id' => $this->db->lastInsertId()
                ];
            } else {
                return ['success' => false, 'message' => 'Failed to add field'];
            }

        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function deleteField($fieldId) {
        try {
            $sql = "DELETE FROM form_fields WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$fieldId]);

            if ($result) {
                return ['success' => true, 'message' => 'Field deleted successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to delete field'];
            }

        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function updateForm($formId, $updates) {
        try {
            $allowedFields = [
                'form_name', 'form_action', 'form_method', 'form_enctype',
                'form_id', 'form_class'
            ];

            $setParts = [];
            $values = [];

            foreach ($updates as $field => $value) {
                if (in_array($field, $allowedFields)) {
                    $setParts[] = "$field = ?";
                    $values[] = $value;
                }
            }

            if (empty($setParts)) {
                return ['success' => false, 'message' => 'No valid fields to update'];
            }

            $values[] = $formId;
            $sql = "UPDATE forms SET " . implode(', ', $setParts) . " WHERE id = ?";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($values);

            if ($result) {
                return ['success' => true, 'message' => 'Form updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update form'];
            }

        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
        }
    }

    public function regenerateHTML($pageId) {
        try {
            // Get page information
            $sql = "SELECT * FROM pages WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
            $page = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$page) {
                return ['success' => false, 'message' => 'Page not found'];
            }

            // Get original HTML content
            $originalContent = file_get_contents($page['file_path']);
            if (!$originalContent) {
                return ['success' => false, 'message' => 'Could not read original file'];
            }

            // Get updated forms and fields
            $sql = "SELECT * FROM forms WHERE page_id = ? ORDER BY form_index";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$pageId]);
            $forms = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // This is a simplified version - in a full implementation,
            // you would need to parse the HTML DOM and update form elements
            // For now, we'll create a backup and log the request

            $backupPath = $page['file_path'] . '.backup.' . time();
            copy($page['file_path'], $backupPath);

            // Log the regeneration request
            $sql = "INSERT INTO analysis_log (page_id, analysis_type, status, message, details) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                'html_regeneration',
                'success',
                'HTML regeneration requested - backup created',
                json_encode(['backup_path' => $backupPath, 'forms_count' => count($forms)])
            ]);

            return [
                'success' => true, 
                'message' => 'HTML regeneration prepared - backup created',
                'backup_path' => $backupPath
            ];

        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Error: ' . $e->getMessage()];
        }
    }
}

// Handle the request
try {
    $modifier = new FormModifier();
    $action = $_POST['action'] ?? $_GET['action'] ?? '';

    switch ($action) {
        case 'update_field':
            $fieldId = $_POST['field_id'] ?? 0;
            $updates = $_POST['updates'] ?? [];
            $result = $modifier->updateField($fieldId, $updates);
            break;

        case 'add_field':
            $formId = $_POST['form_id'] ?? 0;
            $fieldData = $_POST['field_data'] ?? [];
            $result = $modifier->addField($formId, $fieldData);
            break;

        case 'delete_field':
            $fieldId = $_POST['field_id'] ?? 0;
            $result = $modifier->deleteField($fieldId);
            break;

        case 'update_form':
            $formId = $_POST['form_id'] ?? 0;
            $updates = $_POST['updates'] ?? [];
            $result = $modifier->updateForm($formId, $updates);
            break;

        case 'regenerate_html':
            $pageId = $_POST['page_id'] ?? 0;
            $result = $modifier->regenerateHTML($pageId);
            break;

        default:
            $result = ['success' => false, 'message' => 'Invalid action'];
    }

    echo json_encode($result);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
