<?php
/**
 * Setup Script for Webpage Manager
 * Run this script to initialize the application
 */

require_once 'config/database.php';

echo "<h1>Webpage Manager Setup</h1>";

try {
    echo "<h2>Initializing Database...</h2>";
    
    $database = new Database();
    
    // Create database and tables
    if ($database->initializeDatabase()) {
        echo "<p style='color: green;'>✓ Database and tables created successfully!</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create database and tables.</p>";
        exit;
    }
    
    // Check directory permissions
    echo "<h2>Checking Directory Permissions...</h2>";
    
    $directories = [
        'uploads/',
        'uploads/pages/',
        'uploads/assets/'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<p style='color: green;'>✓ Created directory: $dir</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create directory: $dir</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ Directory already exists: $dir</p>";
        }
        
        if (is_writable($dir)) {
            echo "<p style='color: green;'>✓ Directory is writable: $dir</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Directory may not be writable: $dir</p>";
        }
    }
    
    // Test database connection
    echo "<h2>Testing Database Connection...</h2>";
    
    $conn = $database->getConnection();
    if ($conn) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        
        // Test table creation
        $sql = "SELECT COUNT(*) as table_count FROM information_schema.tables 
                WHERE table_schema = 'webpage_manager'";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✓ Found {$result['table_count']} tables in database</p>";
    } else {
        echo "<p style='color: red;'>✗ Database connection failed!</p>";
    }
    
    // Check PHP extensions
    echo "<h2>Checking PHP Extensions...</h2>";
    
    $required_extensions = ['pdo', 'pdo_mysql', 'dom', 'libxml', 'json'];
    
    foreach ($required_extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<p style='color: green;'>✓ Extension loaded: $ext</p>";
        } else {
            echo "<p style='color: red;'>✗ Extension missing: $ext</p>";
        }
    }
    
    // Check file upload settings
    echo "<h2>Checking File Upload Settings...</h2>";
    
    $upload_max = ini_get('upload_max_filesize');
    $post_max = ini_get('post_max_size');
    $max_files = ini_get('max_file_uploads');
    
    echo "<p>Upload max filesize: <strong>$upload_max</strong></p>";
    echo "<p>Post max size: <strong>$post_max</strong></p>";
    echo "<p>Max file uploads: <strong>$max_files</strong></p>";
    
    if (ini_get('file_uploads')) {
        echo "<p style='color: green;'>✓ File uploads are enabled</p>";
    } else {
        echo "<p style='color: red;'>✗ File uploads are disabled</p>";
    }
    
    echo "<h2>Setup Complete!</h2>";
    echo "<p style='color: green; font-size: 18px;'>✓ Webpage Manager is ready to use!</p>";
    echo "<p><a href='index.html' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Launch Application</a></p>";
    
    echo "<h3>Test Files Available:</h3>";
    echo "<ul>";
    echo "<li><a href='test_samples/sample_form.html' target='_blank'>Sample Contact Form</a> - Complex form with various input types</li>";
    echo "<li><a href='test_samples/simple_login.html' target='_blank'>Simple Login Form</a> - Basic login form with CSS</li>";
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Open the application using the link above</li>";
    echo "<li>Go to the 'Import Pages' tab</li>";
    echo "<li>Upload the test HTML files to see the system in action</li>";
    echo "<li>Check the 'Manage Pages' tab to view imported pages</li>";
    echo "<li>Use the 'Database' tab to generate database structures</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Setup failed: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2 {
    color: #333;
}
p {
    margin: 5px 0;
}
ul, ol {
    margin: 10px 0;
}
</style>
