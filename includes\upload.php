<?php
require_once '../config/database.php';
require_once 'html_parser.php';

header('Content-Type: application/json');

class FileUploader {
    private $db;
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->uploadDir = '../uploads/';
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
        
        $this->allowedTypes = [
            'html' => ['text/html', 'application/xhtml+xml'],
            'css' => ['text/css'],
            'js' => ['application/javascript', 'text/javascript'],
            'image' => ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'],
            'font' => ['font/woff', 'font/woff2', 'font/ttf', 'font/otf', 'application/font-woff', 'application/font-woff2']
        ];
    }

    public function handleUpload() {
        if (!isset($_FILES['files'])) {
            return ['success' => false, 'message' => 'No files uploaded'];
        }

        $files = $_FILES['files'];
        $results = [];
        $htmlFiles = [];

        // Process each file
        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $result = $this->processFile([
                    'name' => $files['name'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'size' => $files['size'][$i],
                    'type' => $files['type'][$i]
                ]);
                
                $results[] = $result;
                
                // Keep track of HTML files for form analysis
                if ($result['success'] && $result['file_type'] === 'html') {
                    $htmlFiles[] = $result;
                }
            } else {
                $results[] = [
                    'success' => false,
                    'name' => $files['name'][$i],
                    'message' => $this->getUploadError($files['error'][$i])
                ];
            }
        }

        // Analyze HTML files for forms
        foreach ($htmlFiles as $htmlFile) {
            $this->analyzeHTMLFile($htmlFile['page_id'], $htmlFile['file_path']);
        }

        return [
            'success' => true,
            'message' => 'Upload completed',
            'files' => $results
        ];
    }

    private function processFile($file) {
        // Validate file
        $validation = $this->validateFile($file);
        if (!$validation['valid']) {
            return [
                'success' => false,
                'name' => $file['name'],
                'message' => $validation['message']
            ];
        }

        // Generate unique filename
        $fileInfo = pathinfo($file['name']);
        $extension = strtolower($fileInfo['extension']);
        $baseName = $fileInfo['filename'];
        $uniqueName = $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;

        // Determine file type category
        $fileType = $this->getFileTypeCategory($file['type'], $extension);
        
        // Create directory structure
        $targetDir = $this->uploadDir . ($fileType === 'html' ? 'pages/' : 'assets/');
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }

        $targetPath = $targetDir . $uniqueName;

        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            // Save to database
            $pageId = $this->saveToDatabase($file, $uniqueName, $targetPath, $fileType);
            
            if ($pageId) {
                return [
                    'success' => true,
                    'name' => $file['name'],
                    'message' => 'Uploaded successfully',
                    'file_type' => $fileType,
                    'page_id' => $pageId,
                    'file_path' => $targetPath
                ];
            } else {
                // Remove file if database save failed
                unlink($targetPath);
                return [
                    'success' => false,
                    'name' => $file['name'],
                    'message' => 'Failed to save file information to database'
                ];
            }
        } else {
            return [
                'success' => false,
                'name' => $file['name'],
                'message' => 'Failed to move uploaded file'
            ];
        }
    }

    private function validateFile($file) {
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            return [
                'valid' => false,
                'message' => 'File size exceeds maximum allowed size (10MB)'
            ];
        }

        // Check file type
        $fileInfo = pathinfo($file['name']);
        $extension = strtolower($fileInfo['extension']);
        
        $isValidType = false;
        foreach ($this->allowedTypes as $category => $mimeTypes) {
            if (in_array($file['type'], $mimeTypes) || 
                ($category === 'html' && in_array($extension, ['html', 'htm'])) ||
                ($category === 'css' && $extension === 'css') ||
                ($category === 'js' && $extension === 'js') ||
                ($category === 'image' && in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'])) ||
                ($category === 'font' && in_array($extension, ['woff', 'woff2', 'ttf', 'otf']))) {
                $isValidType = true;
                break;
            }
        }

        if (!$isValidType) {
            return [
                'valid' => false,
                'message' => 'File type not allowed'
            ];
        }

        return ['valid' => true];
    }

    private function getFileTypeCategory($mimeType, $extension) {
        foreach ($this->allowedTypes as $category => $mimeTypes) {
            if (in_array($mimeType, $mimeTypes)) {
                return $category;
            }
        }

        // Fallback to extension-based detection
        switch ($extension) {
            case 'html':
            case 'htm':
                return 'html';
            case 'css':
                return 'css';
            case 'js':
                return 'js';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'svg':
            case 'webp':
                return 'image';
            case 'woff':
            case 'woff2':
            case 'ttf':
            case 'otf':
                return 'font';
            default:
                return 'other';
        }
    }

    private function saveToDatabase($file, $uniqueName, $targetPath, $fileType) {
        try {
            if ($fileType === 'html') {
                // Save as main page
                $title = $this->extractTitle($targetPath);
                
                $sql = "INSERT INTO pages (filename, original_filename, title, file_path, file_size, file_hash, content_type) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $uniqueName,
                    $file['name'],
                    $title,
                    $targetPath,
                    $file['size'],
                    hash_file('sha256', $targetPath),
                    $file['type']
                ]);
                
                return $this->db->lastInsertId();
            } else {
                // Save as associated file (we'll link it to pages later if referenced)
                $sql = "INSERT INTO associated_files (page_id, filename, original_filename, file_path, file_type, file_size, mime_type) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    0, // Will be updated when we detect references
                    $uniqueName,
                    $file['name'],
                    $targetPath,
                    $fileType,
                    $file['size'],
                    $file['type']
                ]);
                
                return $this->db->lastInsertId();
            }
        } catch (PDOException $e) {
            error_log("Database error: " . $e->getMessage());
            return false;
        }
    }

    private function extractTitle($filePath) {
        $content = file_get_contents($filePath);
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $content, $matches)) {
            return trim(strip_tags($matches[1]));
        }
        return null;
    }

    private function analyzeHTMLFile($pageId, $filePath) {
        try {
            $parser = new HTMLParser();
            $forms = $parser->extractForms($filePath);
            
            foreach ($forms as $formIndex => $form) {
                // Save form to database
                $sql = "INSERT INTO forms (page_id, form_name, form_action, form_method, form_enctype, form_id, form_class, form_index) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $pageId,
                    $form['name'] ?? null,
                    $form['action'] ?? null,
                    $form['method'] ?? 'GET',
                    $form['enctype'] ?? null,
                    $form['id'] ?? null,
                    $form['class'] ?? null,
                    $formIndex
                ]);
                
                $formId = $this->db->lastInsertId();
                
                // Save form fields
                if (isset($form['fields'])) {
                    foreach ($form['fields'] as $field) {
                        $sql = "INSERT INTO form_fields (
                            form_id, field_name, field_type, field_id, field_class, field_placeholder, 
                            field_value, field_required, field_readonly, field_disabled, field_multiple,
                            field_min, field_max, field_step, field_pattern, field_maxlength, field_minlength,
                            field_size, field_rows, field_cols, field_accept, field_autocomplete, field_options
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $stmt = $this->db->prepare($sql);
                        $stmt->execute([
                            $formId,
                            $field['name'] ?? '',
                            $field['type'] ?? 'text',
                            $field['id'] ?? null,
                            $field['class'] ?? null,
                            $field['placeholder'] ?? null,
                            $field['value'] ?? null,
                            isset($field['required']) ? 1 : 0,
                            isset($field['readonly']) ? 1 : 0,
                            isset($field['disabled']) ? 1 : 0,
                            isset($field['multiple']) ? 1 : 0,
                            $field['min'] ?? null,
                            $field['max'] ?? null,
                            $field['step'] ?? null,
                            $field['pattern'] ?? null,
                            $field['maxlength'] ?? null,
                            $field['minlength'] ?? null,
                            $field['size'] ?? null,
                            $field['rows'] ?? null,
                            $field['cols'] ?? null,
                            $field['accept'] ?? null,
                            $field['autocomplete'] ?? null,
                            isset($field['options']) ? json_encode($field['options']) : null
                        ]);
                    }
                }
            }
            
            // Log successful analysis
            $sql = "INSERT INTO analysis_log (page_id, analysis_type, status, message, details) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                'form_detection',
                'success',
                'Found ' . count($forms) . ' forms',
                json_encode(['forms_count' => count($forms)])
            ]);
            
        } catch (Exception $e) {
            // Log error
            $sql = "INSERT INTO analysis_log (page_id, analysis_type, status, message) 
                    VALUES (?, ?, ?, ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                $pageId,
                'form_detection',
                'error',
                $e->getMessage()
            ]);
        }
    }

    private function getUploadError($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
}

// Handle the upload
try {
    $uploader = new FileUploader();
    $result = $uploader->handleUpload();
    echo json_encode($result);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Upload failed: ' . $e->getMessage()
    ]);
}
?>
