# WAMP Server Setup Guide for Webpage Manager

## Step 1: Install and Start WAMP

1. **Download WAMP** from [wampserver.com](http://www.wampserver.com/) if not already installed
2. **Start WAMP Server** - click the WAMP icon and start all services
3. **Check Status** - WAMP icon should be GREEN (not orange or red)
4. **Test** - go to `http://localhost` in your browser to see WAMP homepage

## Step 2: Install the Project

1. **Copy Project Files**
   - Copy your entire project folder to: `C:\wamp64\www\webpage-manager\`
   - (Or `C:\wamp\www\webpage-manager\` for 32-bit WAMP)

2. **Set Permissions**
   - Right-click on the `uploads` folder → Properties → Security
   - Make sure "Users" has "Full Control" permissions
   - Or create the uploads folder if it doesn't exist

## Step 3: Database Setup

1. **Access phpMyAdmin**
   - Go to `http://localhost/phpmyadmin`
   - Username: `root`
   - Password: (leave empty for default WAMP)

2. **Create Database** (Optional - the app will create it automatically)
   - Click "New" in phpMyAdmin
   - Database name: `webpage_manager`
   - Collation: `utf8mb4_unicode_ci`
   - Click "Create"

## Step 4: Run the Application

1. **Access Setup Page**
   - Go to: `http://localhost/webpage-manager/setup.php`
   - This will create the database and tables automatically
   - Check for any error messages

2. **Launch Application**
   - Go to: `http://localhost/webpage-manager/`
   - You should see the Webpage Manager interface

## Step 5: Test Upload

1. **Test with Sample Files**
   - Go to: `http://localhost/webpage-manager/test_samples/sample_form.html`
   - Save this file to your computer
   - Upload it using the main application

2. **Debug if Needed**
   - Go to: `http://localhost/webpage-manager/debug_upload.php`
   - This will show system status and help diagnose issues

## Common WAMP Issues & Solutions

### Issue: WAMP Icon is Orange/Red
**Solution:**
- Check if Apache/MySQL services are running
- Check for port conflicts (port 80, 3306)
- Restart WAMP services

### Issue: "Access Denied" Database Error
**Solution:**
- Check MySQL service is running (green in WAMP)
- Verify database credentials in `config/database.php`
- Try accessing phpMyAdmin to test database connection

### Issue: Upload Directory Not Writable
**Solution:**
```
1. Navigate to your project folder in Windows Explorer
2. Right-click "uploads" folder → Properties → Security
3. Click "Edit" → Add "Users" with "Full Control"
4. Apply changes
```

### Issue: File Upload Fails
**Solution:**
- Check PHP settings in WAMP:
  - Click WAMP icon → PHP → PHP Settings
  - Enable "file_uploads"
  - Increase "upload_max_filesize" and "post_max_size"

### Issue: PHP Errors Not Showing
**Solution:**
- Click WAMP icon → PHP → PHP Settings → "display_errors"
- Or edit php.ini: `display_errors = On`

## Recommended WAMP PHP Settings

Access via WAMP icon → PHP → PHP Settings:

- ✅ `file_uploads` - ON
- ✅ `upload_max_filesize` - 20M (or higher)
- ✅ `post_max_size` - 25M (or higher)
- ✅ `max_file_uploads` - 20 (or higher)
- ✅ `display_errors` - ON (for development)

## Project URLs

Once set up, access these URLs:

- **Main Application**: `http://localhost/webpage-manager/`
- **Setup/Install**: `http://localhost/webpage-manager/setup.php`
- **Debug Tool**: `http://localhost/webpage-manager/debug_upload.php`
- **phpMyAdmin**: `http://localhost/phpmyadmin`
- **Sample Form**: `http://localhost/webpage-manager/test_samples/sample_form.html`

## Testing the System

1. **Upload Test Files**:
   - Use the sample files in `test_samples/` folder
   - Try uploading `sample_form.html` and `simple_login.html`

2. **Check Results**:
   - Go to "Manage Pages" tab to see imported pages
   - Click on a page to see detected forms and fields
   - Go to "Database" tab to generate database structure

3. **Verify Database**:
   - Check phpMyAdmin to see created tables
   - Look for tables: `pages`, `forms`, `form_fields`, etc.

## Troubleshooting Commands

If you need to reset everything:

```sql
-- In phpMyAdmin, run this to reset the database:
DROP DATABASE IF EXISTS webpage_manager;
CREATE DATABASE webpage_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

Then run the setup again: `http://localhost/webpage-manager/setup.php`

## Support

If you encounter issues:

1. Check WAMP logs: WAMP icon → Apache → Apache Error Log
2. Check PHP errors in browser console (F12)
3. Use the debug tool: `http://localhost/webpage-manager/debug_upload.php`
4. Verify all services are running (WAMP icon should be green)

The application should work perfectly with WAMP's default configuration!
