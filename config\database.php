<?php
/**
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'webpage_manager';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    public function createDatabase() {
        try {
            // Connect without database name first
            $conn = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $conn->exec($sql);

            return true;
        } catch(PDOException $exception) {
            echo "Database creation error: " . $exception->getMessage();
            return false;
        }
    }

    public function createTables() {
        $conn = $this->getConnection();
        
        if (!$conn) {
            return false;
        }

        try {
            // Pages table
            $sql = "CREATE TABLE IF NOT EXISTS pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                title VARCHAR(500),
                file_path VARCHAR(500) NOT NULL,
                file_size INT NOT NULL,
                file_hash VARCHAR(64) NOT NULL,
                content_type VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_filename (filename),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Forms table
            $sql = "CREATE TABLE IF NOT EXISTS forms (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                form_name VARCHAR(255),
                form_action VARCHAR(500),
                form_method VARCHAR(10) DEFAULT 'GET',
                form_enctype VARCHAR(100),
                form_id VARCHAR(255),
                form_class VARCHAR(255),
                form_index INT NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Form fields table
            $sql = "CREATE TABLE IF NOT EXISTS form_fields (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT NOT NULL,
                field_name VARCHAR(255) NOT NULL,
                field_type VARCHAR(50) NOT NULL,
                field_id VARCHAR(255),
                field_class VARCHAR(255),
                field_placeholder VARCHAR(500),
                field_value TEXT,
                field_required BOOLEAN DEFAULT FALSE,
                field_readonly BOOLEAN DEFAULT FALSE,
                field_disabled BOOLEAN DEFAULT FALSE,
                field_multiple BOOLEAN DEFAULT FALSE,
                field_min VARCHAR(50),
                field_max VARCHAR(50),
                field_step VARCHAR(50),
                field_pattern VARCHAR(500),
                field_maxlength INT,
                field_minlength INT,
                field_size INT,
                field_rows INT,
                field_cols INT,
                field_accept VARCHAR(255),
                field_autocomplete VARCHAR(100),
                field_options TEXT, -- JSON for select options, radio buttons, checkboxes
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_form_id (form_id),
                INDEX idx_field_name (field_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Associated files table (CSS, JS, images, fonts)
            $sql = "CREATE TABLE IF NOT EXISTS associated_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_type VARCHAR(50) NOT NULL, -- css, js, image, font
                file_size INT NOT NULL,
                mime_type VARCHAR(100),
                is_referenced BOOLEAN DEFAULT FALSE, -- whether it's referenced in the HTML
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id),
                INDEX idx_file_type (file_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Generated database tables (for dynamic table creation based on forms)
            $sql = "CREATE TABLE IF NOT EXISTS generated_tables (
                id INT AUTO_INCREMENT PRIMARY KEY,
                table_name VARCHAR(255) NOT NULL UNIQUE,
                form_id INT NOT NULL,
                table_sql TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (form_id) REFERENCES forms(id) ON DELETE CASCADE,
                INDEX idx_form_id (form_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            // Analysis log table
            $sql = "CREATE TABLE IF NOT EXISTS analysis_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                page_id INT NOT NULL,
                analysis_type VARCHAR(50) NOT NULL, -- form_detection, field_analysis, etc.
                status VARCHAR(20) NOT NULL, -- success, error, warning
                message TEXT,
                details JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE CASCADE,
                INDEX idx_page_id (page_id),
                INDEX idx_analysis_type (analysis_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            $conn->exec($sql);

            return true;
        } catch(PDOException $exception) {
            echo "Table creation error: " . $exception->getMessage();
            return false;
        }
    }

    public function initializeDatabase() {
        if ($this->createDatabase() && $this->createTables()) {
            return true;
        }
        return false;
    }
}

// Initialize database on first load
$database = new Database();
if (!$database->initializeDatabase()) {
    die("Failed to initialize database. Please check your database configuration.");
}
?>
