<?php
/**
 * HTML Parser for Form Analysis
 * Extracts forms and input fields from HTML files
 */

class HTMLParser {
    private $dom;
    private $xpath;

    public function __construct() {
        $this->dom = new DOMDocument();
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
    }

    public function extractForms($filePath) {
        $content = file_get_contents($filePath);
        if (!$content) {
            throw new Exception("Could not read file: $filePath");
        }

        // Load HTML content
        $this->dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $this->xpath = new DOMXPath($this->dom);

        $forms = [];
        $formElements = $this->xpath->query('//form');

        foreach ($formElements as $index => $formElement) {
            $forms[] = $this->parseForm($formElement, $index);
        }

        return $forms;
    }

    private function parseForm($formElement, $index) {
        $form = [
            'index' => $index,
            'action' => $formElement->getAttribute('action'),
            'method' => strtoupper($formElement->getAttribute('method')) ?: 'GET',
            'enctype' => $formElement->getAttribute('enctype'),
            'id' => $formElement->getAttribute('id'),
            'class' => $formElement->getAttribute('class'),
            'name' => $formElement->getAttribute('name'),
            'fields' => []
        ];

        // Find all input elements within this form
        $inputElements = $this->xpath->query('.//input | .//textarea | .//select | .//button', $formElement);

        foreach ($inputElements as $inputElement) {
            $field = $this->parseField($inputElement);
            if ($field) {
                $form['fields'][] = $field;
            }
        }

        return $form;
    }

    private function parseField($element) {
        $tagName = strtolower($element->tagName);
        $type = strtolower($element->getAttribute('type'));

        // Skip submit buttons and other non-data fields unless they're important
        if ($tagName === 'button' || ($tagName === 'input' && in_array($type, ['submit', 'reset', 'button', 'image']))) {
            // Only include if they have names (might be used for processing)
            if (!$element->getAttribute('name')) {
                return null;
            }
        }

        $field = [
            'tag' => $tagName,
            'type' => $type ?: ($tagName === 'textarea' ? 'textarea' : ($tagName === 'select' ? 'select' : 'text')),
            'name' => $element->getAttribute('name'),
            'id' => $element->getAttribute('id'),
            'class' => $element->getAttribute('class'),
            'placeholder' => $element->getAttribute('placeholder'),
            'value' => $element->getAttribute('value'),
            'required' => $element->hasAttribute('required'),
            'readonly' => $element->hasAttribute('readonly'),
            'disabled' => $element->hasAttribute('disabled'),
            'multiple' => $element->hasAttribute('multiple')
        ];

        // Add type-specific attributes
        switch ($tagName) {
            case 'input':
                $this->parseInputAttributes($element, $field);
                break;
            case 'textarea':
                $this->parseTextareaAttributes($element, $field);
                break;
            case 'select':
                $this->parseSelectAttributes($element, $field);
                break;
        }

        return $field;
    }

    private function parseInputAttributes($element, &$field) {
        $type = $field['type'];

        // Common attributes for various input types
        $attributes = [
            'min', 'max', 'step', 'pattern', 'maxlength', 'minlength', 
            'size', 'accept', 'autocomplete', 'list'
        ];

        foreach ($attributes as $attr) {
            $value = $element->getAttribute($attr);
            if ($value !== '') {
                $field['field_' . $attr] = $value;
            }
        }

        // Handle special input types
        switch ($type) {
            case 'radio':
            case 'checkbox':
                $field['checked'] = $element->hasAttribute('checked');
                // Find related options (same name)
                if ($field['name']) {
                    $field['options'] = $this->findRelatedOptions($field['name'], $type);
                }
                break;
            
            case 'file':
                $field['accept'] = $element->getAttribute('accept');
                break;
            
            case 'range':
            case 'number':
                $field['min'] = $element->getAttribute('min');
                $field['max'] = $element->getAttribute('max');
                $field['step'] = $element->getAttribute('step');
                break;
            
            case 'email':
            case 'url':
            case 'tel':
                $field['pattern'] = $element->getAttribute('pattern');
                break;
        }
    }

    private function parseTextareaAttributes($element, &$field) {
        $field['rows'] = $element->getAttribute('rows') ?: null;
        $field['cols'] = $element->getAttribute('cols') ?: null;
        $field['maxlength'] = $element->getAttribute('maxlength') ?: null;
        $field['minlength'] = $element->getAttribute('minlength') ?: null;
        $field['wrap'] = $element->getAttribute('wrap') ?: null;
        
        // Get textarea content
        if ($element->textContent) {
            $field['value'] = trim($element->textContent);
        }
    }

    private function parseSelectAttributes($element, &$field) {
        $field['size'] = $element->getAttribute('size') ?: null;
        
        // Extract options
        $options = [];
        $optionElements = $this->xpath->query('.//option', $element);
        
        foreach ($optionElements as $option) {
            $optionData = [
                'value' => $option->getAttribute('value'),
                'text' => trim($option->textContent),
                'selected' => $option->hasAttribute('selected'),
                'disabled' => $option->hasAttribute('disabled')
            ];
            $options[] = $optionData;
        }
        
        // Extract optgroups
        $optgroupElements = $this->xpath->query('.//optgroup', $element);
        foreach ($optgroupElements as $optgroup) {
            $groupOptions = [];
            $groupOptionElements = $this->xpath->query('.//option', $optgroup);
            
            foreach ($groupOptionElements as $option) {
                $groupOptions[] = [
                    'value' => $option->getAttribute('value'),
                    'text' => trim($option->textContent),
                    'selected' => $option->hasAttribute('selected'),
                    'disabled' => $option->hasAttribute('disabled')
                ];
            }
            
            $options[] = [
                'optgroup' => $optgroup->getAttribute('label'),
                'options' => $groupOptions
            ];
        }
        
        $field['options'] = $options;
    }

    private function findRelatedOptions($name, $type) {
        $options = [];
        $elements = $this->xpath->query("//input[@name='$name' and @type='$type']");
        
        foreach ($elements as $element) {
            $options[] = [
                'value' => $element->getAttribute('value'),
                'id' => $element->getAttribute('id'),
                'checked' => $element->hasAttribute('checked'),
                'label' => $this->findLabelForInput($element)
            ];
        }
        
        return $options;
    }

    private function findLabelForInput($inputElement) {
        $inputId = $inputElement->getAttribute('id');
        
        // Try to find label by 'for' attribute
        if ($inputId) {
            $labels = $this->xpath->query("//label[@for='$inputId']");
            if ($labels->length > 0) {
                return trim($labels->item(0)->textContent);
            }
        }
        
        // Try to find parent label
        $parentLabel = $this->xpath->query('ancestor::label', $inputElement);
        if ($parentLabel->length > 0) {
            return trim($parentLabel->item(0)->textContent);
        }
        
        // Try to find adjacent text or label
        $previousSibling = $inputElement->previousSibling;
        while ($previousSibling) {
            if ($previousSibling->nodeType === XML_TEXT_NODE) {
                $text = trim($previousSibling->textContent);
                if ($text) {
                    return $text;
                }
            } elseif ($previousSibling->nodeType === XML_ELEMENT_NODE) {
                if ($previousSibling->tagName === 'label') {
                    return trim($previousSibling->textContent);
                }
                break;
            }
            $previousSibling = $previousSibling->previousSibling;
        }
        
        return null;
    }

    public function extractMetadata($filePath) {
        $content = file_get_contents($filePath);
        if (!$content) {
            throw new Exception("Could not read file: $filePath");
        }

        $this->dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $this->xpath = new DOMXPath($this->dom);

        $metadata = [
            'title' => $this->extractTitle(),
            'description' => $this->extractMetaDescription(),
            'keywords' => $this->extractMetaKeywords(),
            'charset' => $this->extractCharset(),
            'language' => $this->extractLanguage(),
            'stylesheets' => $this->extractStylesheets(),
            'scripts' => $this->extractScripts(),
            'images' => $this->extractImages(),
            'links' => $this->extractLinks()
        ];

        return $metadata;
    }

    private function extractTitle() {
        $titleElements = $this->xpath->query('//title');
        return $titleElements->length > 0 ? trim($titleElements->item(0)->textContent) : null;
    }

    private function extractMetaDescription() {
        $metaElements = $this->xpath->query("//meta[@name='description']");
        return $metaElements->length > 0 ? $metaElements->item(0)->getAttribute('content') : null;
    }

    private function extractMetaKeywords() {
        $metaElements = $this->xpath->query("//meta[@name='keywords']");
        return $metaElements->length > 0 ? $metaElements->item(0)->getAttribute('content') : null;
    }

    private function extractCharset() {
        $metaElements = $this->xpath->query("//meta[@charset]");
        if ($metaElements->length > 0) {
            return $metaElements->item(0)->getAttribute('charset');
        }
        
        $metaElements = $this->xpath->query("//meta[@http-equiv='Content-Type']");
        if ($metaElements->length > 0) {
            $content = $metaElements->item(0)->getAttribute('content');
            if (preg_match('/charset=([^;]+)/i', $content, $matches)) {
                return trim($matches[1]);
            }
        }
        
        return null;
    }

    private function extractLanguage() {
        $htmlElements = $this->xpath->query('//html[@lang]');
        return $htmlElements->length > 0 ? $htmlElements->item(0)->getAttribute('lang') : null;
    }

    private function extractStylesheets() {
        $stylesheets = [];
        $linkElements = $this->xpath->query("//link[@rel='stylesheet']");
        
        foreach ($linkElements as $link) {
            $stylesheets[] = [
                'href' => $link->getAttribute('href'),
                'media' => $link->getAttribute('media'),
                'type' => $link->getAttribute('type')
            ];
        }
        
        return $stylesheets;
    }

    private function extractScripts() {
        $scripts = [];
        $scriptElements = $this->xpath->query('//script[@src]');
        
        foreach ($scriptElements as $script) {
            $scripts[] = [
                'src' => $script->getAttribute('src'),
                'type' => $script->getAttribute('type'),
                'async' => $script->hasAttribute('async'),
                'defer' => $script->hasAttribute('defer')
            ];
        }
        
        return $scripts;
    }

    private function extractImages() {
        $images = [];
        $imgElements = $this->xpath->query('//img[@src]');
        
        foreach ($imgElements as $img) {
            $images[] = [
                'src' => $img->getAttribute('src'),
                'alt' => $img->getAttribute('alt'),
                'width' => $img->getAttribute('width'),
                'height' => $img->getAttribute('height')
            ];
        }
        
        return $images;
    }

    private function extractLinks() {
        $links = [];
        $linkElements = $this->xpath->query('//a[@href]');
        
        foreach ($linkElements as $link) {
            $links[] = [
                'href' => $link->getAttribute('href'),
                'text' => trim($link->textContent),
                'title' => $link->getAttribute('title'),
                'target' => $link->getAttribute('target')
            ];
        }
        
        return $links;
    }

    public function generateDatabaseFieldType($fieldType, $attributes = []) {
        switch (strtolower($fieldType)) {
            case 'email':
                return 'VARCHAR(255)';
            case 'url':
                return 'VARCHAR(500)';
            case 'tel':
            case 'phone':
                return 'VARCHAR(20)';
            case 'number':
            case 'range':
                return isset($attributes['step']) && strpos($attributes['step'], '.') !== false ? 'DECIMAL(10,2)' : 'INT';
            case 'date':
                return 'DATE';
            case 'datetime':
            case 'datetime-local':
                return 'DATETIME';
            case 'time':
                return 'TIME';
            case 'checkbox':
                return 'BOOLEAN';
            case 'textarea':
                return 'TEXT';
            case 'password':
                return 'VARCHAR(255)'; // Will be hashed
            case 'file':
                return 'VARCHAR(500)'; // File path
            case 'hidden':
            case 'text':
            case 'search':
            default:
                $maxLength = isset($attributes['maxlength']) ? (int)$attributes['maxlength'] : 255;
                return $maxLength > 255 ? 'TEXT' : "VARCHAR($maxLength)";
        }
    }
}
?>
